"use client"

import Copy<PERSON>ooltip from "@/app/components/copy-tooltip"
import { CSVExportButton } from "@/components/csv-button-export"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { PaginationWithLinks } from "@/components/ui/pagination-with-links"
import { Progress } from "@/components/ui/progress"
import { Skeleton } from "@/components/ui/skeleton"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { PATH_ROUTER } from "@/constants/routers"
import { formatWeiToEther, formatHash } from "@/helpers/format"
import { useGetTopHolders } from "@/hooks/useTokens"
import { formatTopHoldersForCSV } from "@/lib/utils/csv-export"
import { Alert<PERSON>ir<PERSON>, Download, Users } from "lucide-react"
import Link from "next/link"
import { useRouter, useSearchParams } from "next/navigation"
import { numericFormatter } from "react-number-format"

export function AccountsPageContent() {
  const searchParams = useSearchParams()
  const page = Math.max(1, Number(searchParams.get("page")) || 1)
  const limit = 25
  const router = useRouter()

  const {
    data: holdersResponse,
    isLoading,
    isError,
    error,
  } = useGetTopHolders(page.toString(), limit.toString())

  const holders = holdersResponse?.data || []
  const totalHolders = holdersResponse?.metadata?.total || 0
  const totalPages = holdersResponse?.metadata?.totalPages || 0

  // Handle CSV export
  const handleExport = () => {
    if (holders.length === 0) return
    
    const startIndex = (page - 1) * limit
    const csvData = formatTopHoldersForCSV(holders, startIndex)
    return csvData
  }

  if (isError) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <Users className="h-8 w-8 text-blue-600" />
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Top Token Holders</h1>
              <p className="text-gray-600 mt-1">
                View the top token holders on the Helios blockchain
              </p>
            </div>
          </div>
        </div>

        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <AlertCircle className="h-12 w-12 text-red-500 mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Failed to Load Data
            </h3>
            <p className="text-gray-600 text-center mb-4">
              {error?.message || "Unable to fetch top token holders data"}
            </p>
            <Button
              onClick={() => window.location.reload()}
              variant="outline"
            >
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <Users className="h-8 w-8 text-blue-600" />
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Top Token Holders</h1>
            <p className="text-gray-600 mt-1">
              {isLoading ? (
                <Skeleton className="h-5 w-64" />
              ) : (
                `Showing ${holders.length} of ${totalHolders.toLocaleString()} token holders`
              )}
            </p>
          </div>
        </div>

        {!isLoading && holders.length > 0 && (
          <CSVExportButton
            data={handleExport()}
            filename={`top-token-holders-page-${page}.csv`}
            className="flex items-center gap-2"
          >
            <Download className="h-4 w-4" />
            Export CSV
          </CSVExportButton>
        )}
      </div>

      {isLoading ? (
        <LoadingSkeleton />
      ) : holders.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Users className="h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              No Token Holders Found
            </h3>
            <p className="text-gray-600 text-center">
              No token holder data is currently available.
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          <Card>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[80px] font-medium">Rank</TableHead>
                    <TableHead className="min-w-[400px] font-medium">Address</TableHead>
                    <TableHead className="w-[200px] text-right font-medium">
                      Balance
                    </TableHead>
                    <TableHead className="w-[200px] text-right font-medium">
                      Percentage
                    </TableHead>
                    <TableHead className="w-[200px] text-right font-medium">
                      Txn Count
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {holders.map((holder, index) => (
                    <TableRow key={holder.id} className="hover:bg-gray-50/50">
                      <TableCell className="w-[80px] py-4 text-left font-medium text-gray-900">
                        {(page - 1) * limit + index + 1}
                      </TableCell>
                      <TableCell className="min-w-[400px] py-4">
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <span
                                className="font-mono text-sm text-blue-600 hover:text-blue-800 cursor-pointer transition-colors"
                                onClick={() => {
                                  router.push(PATH_ROUTER.ADDRESS_DETAIL(holder.address))
                                }}
                              >
                                {formatHash(holder.address)}
                              </span>
                            </TooltipTrigger>
                            <TooltipContent>
                              <div className="flex items-center gap-2">
                                <span className="font-mono text-xs">
                                  {holder.address}
                                </span>
                                <CopyTooltip text={holder.address} />
                              </div>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </TableCell>
                      <TableCell className="w-[200px] py-4 text-right">
                        <div className="flex flex-col items-end">
                          <span className="font-mono text-sm font-medium">
                            {numericFormatter(
                              formatWeiToEther(holder.balance, 18),
                              {
                                thousandSeparator: true,
                                decimalScale: 6,
                                fixedDecimalScale: false,
                              },
                            )}
                          </span>
                          <span className="text-xs text-gray-500">HLS</span>
                        </div>
                      </TableCell>
                      <TableCell className="w-[200px] py-4 text-right">
                        <div className="flex flex-col items-end gap-1">
                          <span className="font-mono text-sm font-medium">
                            {holder.percentage ? `${holder.percentage}%` : "N/A"}
                          </span>
                          <Progress
                            value={Math.min(
                              Number.parseFloat(holder.percentage || "0"),
                              100,
                            )}
                            className="h-1 w-full max-w-[180px] bg-gray-200"
                            indicatorClassName="bg-blue-500"
                          />
                        </div>
                      </TableCell>
                      <TableCell className="w-[200px] py-4 text-right font-mono text-sm">
                        {holder.txnCount.toLocaleString()}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </Card>

          <PaginationWithLinks
            page={page}
            pageSize={limit}
            totalCount={totalHolders}
            pageSearchParam="page"
            baseUrl={PATH_ROUTER.ACCOUNTS}
          />
        </div>
      )}
    </div>
  )
}

function LoadingSkeleton() {
  return (
    <Card>
      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[80px]">Rank</TableHead>
              <TableHead className="min-w-[400px]">Address</TableHead>
              <TableHead className="w-[200px] text-right">Balance</TableHead>
              <TableHead className="w-[200px] text-right">Percentage</TableHead>
              <TableHead className="w-[200px] text-right">Txn Count</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {Array.from({ length: 10 }).map((_, index) => (
              <TableRow key={index}>
                <TableCell>
                  <Skeleton className="h-5 w-8" />
                </TableCell>
                <TableCell>
                  <Skeleton className="h-5 w-80" />
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex flex-col items-end gap-1">
                    <Skeleton className="h-5 w-24" />
                    <Skeleton className="h-3 w-12" />
                  </div>
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex flex-col items-end gap-1">
                    <Skeleton className="h-5 w-16" />
                    <Skeleton className="h-1 w-32" />
                  </div>
                </TableCell>
                <TableCell className="text-right">
                  <Skeleton className="h-5 w-16" />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </Card>
  )
}
